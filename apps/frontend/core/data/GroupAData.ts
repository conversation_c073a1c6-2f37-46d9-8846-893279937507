/**
 * 矩阵系统数据管理 - A-M组完整数据系统
 *
 * 🎯 核心价值：33×33矩阵的完整数据源，支持9种颜色和4个层级，A-M组偏移系统
 * 📦 功能范围：坐标映射、颜色分组、层级管理、组偏移、数据查询、验证统计
 * 🔄 架构设计：基于A组基础数据和偏移配置的高性能数据结构
 *
 * <AUTHOR> System
 * @version 2.0.0
 */

import type {
  BasicColorType,
  ColorValue,
  DataLevel,
  GroupOffsetConfig,
  GroupType
} from '../matrix/MatrixTypes';

import {
  MATRIX_SIZE,
  coordinateKey
} from '../matrix/MatrixTypes';

// ===== 核心数据类型 =====

/** 矩阵数据点 - 单个数据点的完整信息 */
export interface MatrixDataPoint {
  /** X坐标 (0-32) */
  x: number;
  /** Y坐标 (0-32) */
  y: number;
  /** 颜色类型 */
  color: BasicColorType;
  /** 数据级别 (1-4) */
  level: DataLevel;
  /** 所属组 (A-M) */
  group: GroupType;
  /** 唯一标识符 */
  id: string;
}

/** 矩阵数据集合 - 包含所有数据点和索引的完整数据结构 */
export interface MatrixDataSet {
  /** 所有数据点的数组 */
  points: MatrixDataPoint[];
  /** 按颜色分组的索引 */
  byColor: Map<BasicColorType, MatrixDataPoint[]>;
  /** 按级别分组的索引 */
  byLevel: Map<DataLevel, MatrixDataPoint[]>;
  /** 按组分组的索引 */
  byGroup: Map<GroupType, MatrixDataPoint[]>;
  /** 按坐标索引的映射 */
  byCoordinate: Map<string, MatrixDataPoint>;
  /** 元数据信息 */
  metadata: MatrixDataSetMetadata;
}

/** 数据集元数据 */
interface MatrixDataSetMetadata {
  /** 总数据点数量 */
  totalPoints: number;
  /** 各颜色数据点数量统计 */
  colorCounts: Record<BasicColorType, number>;
  /** 各级别数据点数量统计 */
  levelCounts: Record<DataLevel, number>;
  /** 各组数据点数量统计 */
  groupCounts: Record<GroupType, number>;
  /** 最后更新时间戳 */
  lastUpdate: number;
}

// ===== A组基础数据结构 =====

/** A组基础数据结构 - 使用网格坐标 (0-32) 直接存储，便于高效匹配 */
const _GROUP_A_DATA_RAW = {
  black: {
    1: [[16, 16] as [number, number]]  // 原显示坐标 [0, 0] -> 网格坐标 [16, 16]
  },
  red: {
    1: [[24, 16] as [number, number]],  // 原显示坐标 [8, 0] -> 网格坐标 [24, 16]
    2: [[20, 16] as [number, number]],  // 原显示坐标 [4, 0] -> 网格坐标 [20, 16]
    3: [[18, 16], [22, 16], [20, 18], [20, 14]] as [number, number][],  // 原显示坐标 [2, 0], [6, 0], [4, 2], [4, -2]
    4: [[17, 16], [19, 16], [21, 16], [23, 16], [18, 17], [18, 15], [19, 18], [19, 14], [20, 17], [20, 19], [20, 15], [20, 13], [22, 17], [22, 15], [21, 18], [21, 14]] as [number, number][]
  },
  orange: {
    1: [[20, 20] as [number, number]],  // 原显示坐标 [4, 4] -> 网格坐标 [20, 20]
    3: [[14, 22], [18, 18], [22, 14]] as [number, number][],  // 原显示坐标 [-2, 6], [2, 2], [6, -2]
    4: [[15, 23], [15, 21], [13, 21], [19, 19], [19, 17], [17, 17], [17, 19], [23, 15], [21, 13], [21, 15]] as [number, number][]
  },
  yellow: {
    1: [[16, 24] as [number, number]],  // 原显示坐标 [0, 8] -> 网格坐标 [16, 24]
    2: [[16, 20] as [number, number]],  // 原显示坐标 [0, 4] -> 网格坐标 [16, 20]
    3: [[16, 18], [16, 22], [18, 20], [14, 20]] as [number, number][],  // 原显示坐标 [0, 2], [0, 6], [2, 4], [-2, 4]
    4: [[16, 17], [16, 19], [16, 21], [16, 23], [17, 18], [17, 20], [17, 22], [15, 18], [15, 20], [15, 22], [18, 19], [18, 21], [14, 19], [14, 21], [13, 20], [19, 20]] as [number, number][]
  },
  green: {
    1: [[12, 20] as [number, number]],  // 原显示坐标 [-4, 4] -> 网格坐标 [12, 20]
    3: [[18, 22], [14, 18], [10, 14]] as [number, number][],  // 原显示坐标 [2, 6], [-2, 2], [-6, -2]
    4: [[17, 23], [19, 21], [17, 21], [13, 19], [15, 19], [15, 17], [13, 17], [9, 15], [11, 15], [11, 13]] as [number, number][]
  },
  cyan: {
    1: [[8, 16] as [number, number]],   // 原显示坐标 [-8, 0] -> 网格坐标 [8, 16]
    2: [[12, 16] as [number, number]],  // 原显示坐标 [-4, 0] -> 网格坐标 [12, 16]
    3: [[14, 16], [10, 16], [12, 18], [12, 14]] as [number, number][],  // 原显示坐标 [-2, 0], [-6, 0], [-4, 2], [-4, -2]
    4: [[9, 16], [11, 16], [13, 16], [15, 16], [10, 17], [10, 15], [11, 18], [11, 14], [12, 17], [12, 19], [12, 15], [12, 13], [14, 17], [14, 15], [13, 18], [13, 14]] as [number, number][]
  },
  blue: {
    1: [[12, 12] as [number, number]],  // 原显示坐标 [-4, -4] -> 网格坐标 [12, 12]
    3: [[10, 18], [14, 14], [18, 10]] as [number, number][],  // 原显示坐标 [-6, 2], [-2, -2], [2, -6]
    4: [[11, 19], [9, 17], [11, 17], [15, 15], [13, 15], [13, 13], [15, 13], [19, 11], [17, 11], [17, 9]] as [number, number][]
  },
  purple: {
    1: [[16, 8] as [number, number]],   // 原显示坐标 [0, -8] -> 网格坐标 [16, 8]
    2: [[16, 12] as [number, number]],  // 原显示坐标 [0, -4] -> 网格坐标 [16, 12]
    3: [[16, 14], [16, 10], [18, 12], [14, 12]] as [number, number][],  // 原显示坐标 [0, -2], [0, -6], [2, -4], [-2, -4]
    4: [[16, 15], [15, 14], [16, 13], [17, 14], [14, 13], [13, 12], [14, 11], [15, 12], [18, 13], [17, 12], [18, 11], [19, 12], [16, 11], [15, 10], [16, 9], [17, 10]] as [number, number][]
  },
  pink: {
    1: [[20, 12] as [number, number]],  // 原显示坐标 [4, -4] -> 网格坐标 [20, 12]
    3: [[22, 18], [18, 14], [14, 10]] as [number, number][],  // 原显示坐标 [6, 2], [2, -2], [-2, -6]
    4: [[13, 11], [15, 9], [15, 11], [17, 15], [17, 13], [19, 13], [19, 15], [21, 19], [23, 17], [21, 17]] as [number, number][]
  }
} as const;

/** A组基础数据结构 - 深度冻结的不可变数据 */
export const GROUP_A_DATA = Object.freeze(
  Object.fromEntries(
    Object.entries(_GROUP_A_DATA_RAW).map(([color, levels]) => [
      color,
      Object.freeze(
        Object.fromEntries(
          Object.entries(levels).map(([level, coords]) => [
            level,
            Object.freeze(coords.map(coord => Object.freeze(coord)))
          ])
        )
      )
    ])
  )
) as typeof _GROUP_A_DATA_RAW;

// ===== 可用级别映射 =====

/** 可用级别映射 - 统一的权威数据源 */
const _AVAILABLE_LEVELS_RAW = {
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
  black: [1], // 黑色只有一个级别
} as const;

/** 可用级别映射 - 深度冻结的不可变数据 */
export const AVAILABLE_LEVELS: Record<BasicColorType, readonly number[]> = Object.freeze(
  Object.fromEntries(
    Object.entries(_AVAILABLE_LEVELS_RAW).map(([color, levels]) => [
      color,
      Object.freeze([...levels])
    ])
  )
) as Record<BasicColorType, readonly number[]>;

// ===== 默认颜色值定义 =====

/** 默认颜色值与颜色数字映射 */
const _DEFAULT_COLOR_VALUES_RAW = {
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0], mappingValue: 'group' as const }, // 黑色使用组字符映射
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 as const },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 as const },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 as const },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 as const },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 as const },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 as const },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 as const },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 as const },
} as const;

/** 默认颜色值 - 深度冻结的不可变数据 */
export const DEFAULT_COLOR_VALUES: Record<BasicColorType, ColorValue> = Object.freeze(
  Object.fromEntries(
    Object.entries(_DEFAULT_COLOR_VALUES_RAW).map(([color, value]) => [
      color,
      Object.freeze({
        ...value,
        rgb: Object.freeze([...value.rgb]),
        hsl: Object.freeze([...value.hsl])
      })
    ])
  )
) as Record<BasicColorType, ColorValue>;

// ===== 矩阵计算常量 =====

/** 最大数据级别 */
export const MAX_LEVEL = 4;

/** 网格尺寸常量 - 基于MATRIX_SIZE计算 */
export const GRID_DIMENSIONS = {
  ROWS: MATRIX_SIZE,
  COLS: MATRIX_SIZE
} as const;

/** 网格中心点坐标 */
export const GRID_CENTER = {
  X: Math.floor(MATRIX_SIZE / 2), // 中心列索引
  Y: Math.floor(MATRIX_SIZE / 2)  // 中心行索引
} as const;

// ===== A到M组偏移配置 =====

/**
 * 创建统一偏移配置 - 所有颜色使用相同偏移值
 * @param offset 偏移值 [x, y]
 * @returns 统一偏移配置
 */
const createUniformOffsetConfig = (offset: [number, number]): GroupOffsetConfig => ({
  defaultOffset: offset,
  level1Offsets: {
    red: offset, orange: offset, yellow: offset, purple: offset,
    green: offset, cyan: offset, blue: offset, pink: offset, black: offset
  }
});

/** A到M组偏移配置 - 基于对称性和规律性设计 */
export const GROUP_OFFSET_CONFIGS: Record<GroupType, GroupOffsetConfig> = {
  // 基础组 - A组无偏移，作为所有其他组的基础
  A: {
    defaultOffset: [0, 0]
  },

  // 方向偏移组 - 基于四个主要方向
  B: {
    defaultOffset: [16, 0], // 向右偏移
    level1Offsets: {
      red: [0, 0], orange: [8, 8], yellow: [16, 16], green: [24, 0], cyan: [32, 0],
      blue: [24, 0], purple: [16, -16], pink: [8, -8], black: [16, 0]
    }
  },
  C: {
    defaultOffset: [-16, 0], // 向左偏移
    level1Offsets: {
      red: [32, 0], orange: [-24, 8], yellow: [-16, 16], green: [-8, 8], cyan: [0, 0],
      blue: [-8, -8], purple: [-16, -16], pink: [-24, -8], black: [-16, 0]
    }
  },
  D: {
    defaultOffset: [0, 16], // 向下偏移
    level1Offsets: {
      red: [-16, 16], orange: [-8, 24], yellow: [0, 32], green: [8, 24],
      cyan: [16, 16], blue: [8, 8], purple: [0, 0], pink: [-8, 8], black: [0, 16]
    }
  },
  E: {
    defaultOffset: [0, -16], // 向上偏移
    level1Offsets: {
      red: [-16, -16], orange: [-8, -8], yellow: [0, -16], green: [8, -8],
      cyan: [16, -16], blue: [8, -24], purple: [-32, 0], pink: [-8, -24], black: [0, -16]
    }
  },

  // 对角偏移组 - 基于四个对角方向
  F: {
    defaultOffset: [8, 8], // 右下偏移
    level1Offsets: {
      red: [0, 0], orange: [8, 8], yellow: [16, 16], green: [16, 16], cyan: [16, 16],
      blue: [8, 8], purple: [0, 0], pink: [0, 0], black: [8, 8]
    }
  },
  G: {
    defaultOffset: [-8, -8], // 左上偏移
    level1Offsets: {
      red: [-16, -16], orange: [-8, -8], yellow: [0, 0], green: [0, 0], cyan: [0, 0],
      blue: [-8, -8], purple: [-16, -16], pink: [-16, -16], black: [-8, -8]
    }
  },
  H: {
    defaultOffset: [8, -8], // 右上偏移
    level1Offsets: {
      red: [0, 0], orange: [0, 0], yellow: [0, 0], green: [8, -8], cyan: [16, -16],
      blue: [16, -16], purple: [16, -16], pink: [8, -8], black: [8, -8]
    }
  },
  I: {
    defaultOffset: [-8, 8], // 左下偏移
    level1Offsets: {
      red: [-16, 16], orange: [-16, 16], yellow: [-16, 16], green: [-8, 8], cyan: [0, 0],
      blue: [0, 0], purple: [0, 0], pink: [-8, 8], black: [-8, 8]
    }
  },

  // 统一偏移组 - 所有颜色使用相同偏移值，便于批量处理
  J: createUniformOffsetConfig([16, 16]),   // 右下大偏移
  K: createUniformOffsetConfig([-16, -16]), // 左上大偏移
  L: createUniformOffsetConfig([16, -16]),  // 右上大偏移
  M: createUniformOffsetConfig([-16, 16])   // 左下大偏移
};

// ===== 通用工具函数 =====

/**
 * 通用初始化函数 - 创建指定键的计数器对象
 * @param keys 键数组
 * @param defaultValue 默认值
 * @returns 初始化的计数器对象
 */
function createCounterObject<T extends string | number>(
  keys: readonly T[],
  defaultValue: number = 0
): Record<T, number> {
  return Object.fromEntries(
    keys.map(key => [key, defaultValue])
  ) as Record<T, number>;
}

/**
 * 向Map中的数组添加元素，如果键不存在则创建新数组
 * @param map 目标Map
 * @param key 键
 * @param value 要添加的值
 */
function addToMapArray<K, V>(map: Map<K, V[]>, key: K, value: V): void {
  const existing = map.get(key);
  if (existing) {
    existing.push(value);
  } else {
    map.set(key, [value]);
  }
}

/**
 * 安全的数组访问函数
 * @param map Map对象
 * @param key 键
 * @returns 数组或空数组
 */
function safeGetArray<K, V>(map: Map<K, V[]>, key: K): V[] {
  return map.get(key) ?? [];
}

// ===== 数据处理工具函数 =====

/** 将显示坐标转换为绝对网格坐标 (保留此函数用于其他用途) */
export const toAbsoluteCoordinate = (displayX: number, displayY: number): [number, number] => {
  return [GRID_CENTER.X + displayX, GRID_CENTER.Y + displayY];
};

/**
 * 将网格坐标转换为以(16,16)为中心点(0,0)的显示坐标
 * @param gridX 网格X坐标 (0-32)
 * @param gridY 网格Y坐标 (0-32)
 * @returns 显示坐标 [displayX, displayY]，范围(-16到16)
 */
export const toDisplayCoordinate = (gridX: number, gridY: number): [number, number] => {
  return [gridX - GRID_CENTER.X, gridY - GRID_CENTER.Y];
};

/**
 * 将显示坐标转换为网格坐标
 * @param displayX 显示X坐标 (-16到16)
 * @param displayY 显示Y坐标 (-16到16)
 * @returns 网格坐标 [gridX, gridY]，范围(0-32)
 */
export const fromDisplayCoordinate = (displayX: number, displayY: number): [number, number] => {
  return [displayX + GRID_CENTER.X, displayY + GRID_CENTER.Y];
};

/** 应用偏移到坐标 */
export const applyOffset = (x: number, y: number, offset: [number, number]): [number, number] => {
  return [x + offset[0], y + offset[1]];
};

/** 检查网格坐标是否在有效范围内 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  return x >= 0 && x < MATRIX_SIZE && y >= 0 && y < MATRIX_SIZE;
};

/** 检查显示坐标是否在有效范围内 */
export const isValidDisplayCoordinate = (displayX: number, displayY: number): boolean => {
  const [gridX, gridY] = fromDisplayCoordinate(displayX, displayY);
  return isValidCoordinate(gridX, gridY);
};

// ===== 数据生成函数 =====

/**
 * 从A组基础数据生成指定组的数据点
 * @param group 目标组类型，默认为'A'
 * @returns 生成的数据点数组
 * @throws {Error} 当组类型无效时抛出错误
 */
export const generateGroupData = (group: GroupType = 'A'): MatrixDataPoint[] => {
  // 输入验证
  if (!ALL_GROUPS.includes(group)) {
    throw new Error(`无效的组类型: ${group}. 有效值为: ${ALL_GROUPS.join(', ')}`);
  }

  const points: MatrixDataPoint[] = [];
  const config = GROUP_OFFSET_CONFIGS[group];

  if (!config) {
    throw new Error(`组 ${group} 的偏移配置未找到`);
  }

  // 使用 try-catch 包装数据生成过程
  try {
    // 遍历所有颜色和级别，生成数据点
    for (const [colorKey, levels] of Object.entries(GROUP_A_DATA)) {
      const color = colorKey as BasicColorType;

      for (const [levelKey, coordinates] of Object.entries(levels)) {
        const level = parseInt(levelKey) as DataLevel;

        // 验证级别有效性
        if (!ALL_LEVELS.includes(level)) {
          console.warn(`跳过无效级别 ${level} for color ${color}`);
          continue;
        }

        // 为当前颜色和级别的每个坐标生成数据点
        coordinates.forEach((gridCoord, index) => {
          try {
            const dataPoint = createDataPoint(
              gridCoord,
              color,
              level,
              group,
              config,
              index
            );

            if (dataPoint) {
              points.push(dataPoint);
            }
          } catch (error) {
            console.warn(`创建数据点失败: ${error instanceof Error ? error.message : String(error)}`);
          }
        });
      }
    }
  } catch (error) {
    throw new Error(`生成组 ${group} 数据时发生错误: ${error instanceof Error ? error.message : String(error)}`);
  }

  return points;
};

/**
 * 创建单个数据点
 * @param gridCoord 网格坐标 (已经是绝对坐标)
 * @param color 颜色类型
 * @param level 数据级别
 * @param group 组类型
 * @param config 组偏移配置
 * @param index 坐标索引
 * @returns 数据点对象，如果坐标无效则返回null
 */
function createDataPoint(
  gridCoord: [number, number],
  color: BasicColorType,
  level: DataLevel,
  group: GroupType,
  config: GroupOffsetConfig,
  index: number
): MatrixDataPoint | null {
  // A组数据现在直接使用网格坐标，无需转换
  const [baseX, baseY] = gridCoord;

  // 确定偏移值：level1使用特殊偏移，其他级别使用默认偏移
  const offset = (config.level1Offsets && level === 1)
    ? config.level1Offsets[color] || config.defaultOffset
    : config.defaultOffset;

  // 应用偏移
  const [finalX, finalY] = applyOffset(baseX, baseY, offset);

  // 验证坐标有效性
  if (!isValidCoordinate(finalX, finalY)) {
    return null;
  }

  return {
    x: finalX,
    y: finalY,
    color,
    level,
    group,
    id: generateDataPointId(group, level, color, index)
  };
}

/**
 * 生成数据点唯一标识符
 * @param group 组类型
 * @param level 数据级别
 * @param color 颜色类型
 * @param index 索引
 * @returns 唯一标识符
 */
function generateDataPointId(
  group: GroupType,
  level: DataLevel,
  color: BasicColorType,
  index: number
): string {
  return `${group.toLowerCase()}${level}-${color}-${index + 1}`;
}

/**
 * 性能监控接口
 */
interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  pointsGenerated: number;
  groupsProcessed: number;
}

/**
 * 创建完整的矩阵数据集（优化版本）
 * @param groups 要包含的组类型数组，默认为['A']
 * @returns 完整的矩阵数据集，包含所有数据点和索引
 * @throws {Error} 当输入参数无效时抛出错误
 */
export const createMatrixDataSet = (groups: GroupType[] = ['A']): MatrixDataSet => {
  // 性能监控
  const metrics: PerformanceMetrics = {
    startTime: performance.now(),
    pointsGenerated: 0,
    groupsProcessed: 0
  };

  try {
    // 输入验证
    if (!Array.isArray(groups) || groups.length === 0) {
      throw new Error('组数组不能为空');
    }

    // 验证所有组类型
    const invalidGroups = groups.filter(group => !ALL_GROUPS.includes(group));
    if (invalidGroups.length > 0) {
      throw new Error(`无效的组类型: ${invalidGroups.join(', ')}`);
    }

    // 去重并排序组数组以提高缓存效率
    const uniqueGroups = [...new Set(groups)].sort();

    // 生成所有组的数据点
    const allPoints = generateAllGroupsData(uniqueGroups);
    metrics.pointsGenerated = allPoints.length;
    metrics.groupsProcessed = uniqueGroups.length;

    // 创建索引映射
    const indices = createDataIndices(allPoints);

    // 生成统计信息
    const metadata = generateMetadata(allPoints);

    // 记录性能指标
    metrics.endTime = performance.now();
    metrics.duration = metrics.endTime - metrics.startTime;

    // 在开发环境下输出性能信息
    if (process.env.NODE_ENV === 'development' && metrics.duration > 10) {
      console.log(`数据集创建性能指标:`, {
        duration: `${metrics.duration.toFixed(2)}ms`,
        pointsGenerated: metrics.pointsGenerated,
        groupsProcessed: metrics.groupsProcessed,
        pointsPerMs: (metrics.pointsGenerated / metrics.duration).toFixed(2)
      });
    }

    return {
      points: allPoints,
      ...indices,
      metadata
    };
  } catch (error) {
    metrics.endTime = performance.now();
    metrics.duration = metrics.endTime - metrics.startTime;

    throw new Error(`创建矩阵数据集失败: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * 生成所有指定组的数据点
 * @param groups 组类型数组
 * @returns 所有数据点的数组
 */
function generateAllGroupsData(groups: GroupType[]): MatrixDataPoint[] {
  const allPoints: MatrixDataPoint[] = [];

  for (const group of groups) {
    const groupPoints = generateGroupData(group);
    allPoints.push(...groupPoints);
  }

  return allPoints;
}

/**
 * 创建数据索引映射
 * @param points 数据点数组
 * @returns 索引映射对象
 */
function createDataIndices(points: MatrixDataPoint[]) {
  const byColor = new Map<BasicColorType, MatrixDataPoint[]>();
  const byLevel = new Map<DataLevel, MatrixDataPoint[]>();
  const byGroup = new Map<GroupType, MatrixDataPoint[]>();
  const byCoordinate = new Map<string, MatrixDataPoint>();

  for (const point of points) {
    // 按颜色分组
    addToMapArray(byColor, point.color, point);

    // 按级别分组
    addToMapArray(byLevel, point.level, point);

    // 按组分组
    addToMapArray(byGroup, point.group, point);

    // 按坐标索引（坐标唯一，直接设置）
    const key = coordinateKey(point.x, point.y);
    byCoordinate.set(key, point);
  }

  return { byColor, byLevel, byGroup, byCoordinate };
}



/**
 * 生成数据集元数据
 * @param points 数据点数组
 * @returns 元数据对象
 */
function generateMetadata(points: MatrixDataPoint[]): MatrixDataSetMetadata {
  // 初始化计数器
  const colorCounts = initializeColorCounts();
  const levelCounts = initializeLevelCounts();
  const groupCounts = initializeGroupCounts();

  // 统计各类数据
  for (const point of points) {
    colorCounts[point.color]++;
    levelCounts[point.level]++;
    groupCounts[point.group]++;
  }

  return {
    totalPoints: points.length,
    colorCounts,
    levelCounts,
    groupCounts,
    lastUpdate: Date.now()
  };
}

/** 所有颜色类型的常量数组 */
const ALL_COLORS: readonly BasicColorType[] = Object.freeze([
  'black', 'red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'
] as const);

/** 所有数据级别的常量数组 */
const ALL_LEVELS: readonly DataLevel[] = Object.freeze([1, 2, 3, 4] as const);

/** 所有组类型的常量数组 */
const ALL_GROUPS: readonly GroupType[] = Object.freeze([
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'
] as const);

/** 初始化颜色计数器 */
const initializeColorCounts = (): Record<BasicColorType, number> =>
  createCounterObject(ALL_COLORS);

/** 初始化级别计数器 */
const initializeLevelCounts = (): Record<DataLevel, number> =>
  createCounterObject(ALL_LEVELS);

/** 初始化组计数器 */
const initializeGroupCounts = (): Record<GroupType, number> =>
  createCounterObject(ALL_GROUPS);

// ===== 数据查询函数 =====

/**
 * 根据坐标获取数据点
 * @param dataSet 数据集
 * @param x X坐标
 * @param y Y坐标
 * @returns 数据点对象，如果不存在则返回null
 */
export const getMatrixDataByCoordinate = (
  dataSet: MatrixDataSet,
  x: number,
  y: number
): MatrixDataPoint | null => {
  const key = coordinateKey(x, y);
  return dataSet.byCoordinate.get(key) ?? null;
};

/**
 * 根据颜色获取数据点
 * @param dataSet 数据集
 * @param color 颜色类型
 * @returns 该颜色的所有数据点数组
 */
export const getMatrixDataByColor = (
  dataSet: MatrixDataSet,
  color: BasicColorType
): MatrixDataPoint[] => {
  return safeGetArray(dataSet.byColor, color);
};

/**
 * 根据级别获取数据点
 * @param dataSet 数据集
 * @param level 数据级别
 * @returns 该级别的所有数据点数组
 */
export const getMatrixDataByLevel = (
  dataSet: MatrixDataSet,
  level: DataLevel
): MatrixDataPoint[] => {
  return safeGetArray(dataSet.byLevel, level);
};

/**
 * 根据组获取数据点
 * @param dataSet 数据集
 * @param group 组类型
 * @returns 该组的所有数据点数组
 */
export const getMatrixDataByGroup = (
  dataSet: MatrixDataSet,
  group: GroupType
): MatrixDataPoint[] => {
  return safeGetArray(dataSet.byGroup, group);
};

/**
 * 检查指定坐标是否包含数据
 * @param dataSet 数据集
 * @param x X坐标
 * @param y Y坐标
 * @returns 如果坐标包含数据则返回true
 */
export const hasMatrixData = (
  dataSet: MatrixDataSet,
  x: number,
  y: number
): boolean => {
  const key = coordinateKey(x, y);
  return dataSet.byCoordinate.has(key);
};

/**
 * 获取多个组的数据点
 * @param dataSet 数据集
 * @param groups 组类型数组
 * @returns 所有指定组的数据点数组
 */
export const getMatrixDataByGroups = (
  dataSet: MatrixDataSet,
  groups: GroupType[]
): MatrixDataPoint[] => {
  const result: MatrixDataPoint[] = [];
  for (const group of groups) {
    result.push(...getMatrixDataByGroup(dataSet, group));
  }
  return result;
};

/**
 * 根据条件过滤数据点
 * @param dataSet 数据集
 * @param predicate 过滤条件函数
 * @returns 符合条件的数据点数组
 */
export const filterMatrixData = (
  dataSet: MatrixDataSet,
  predicate: (point: MatrixDataPoint) => boolean
): MatrixDataPoint[] => {
  return dataSet.points.filter(predicate);
};

/**
 * 获取数据点的实际映射值
 * @param point 数据点
 * @returns 映射值（数字或组字符）
 */
export const getDataPointMappingValue = (point: MatrixDataPoint): string | number => {
  const colorValue = DEFAULT_COLOR_VALUES[point.color];

  if (colorValue.mappingValue === 'group') {
    // 黑色使用组字符
    return point.group;
  }

  // 其他颜色使用数字映射值
  return colorValue.mappingValue ?? point.level;
};

/**
 * 获取颜色的映射值（静态）
 * @param color 颜色类型
 * @returns 映射值配置
 */
export const getColorMappingValue = (color: BasicColorType): number | 'group' | undefined => {
  return DEFAULT_COLOR_VALUES[color].mappingValue;
};

/**
 * 获取数据点的坐标信息（包含网格坐标和显示坐标）
 * @param point 数据点
 * @returns 坐标信息对象
 */
export const getDataPointCoordinateInfo = (point: MatrixDataPoint) => {
  const gridCoord: [number, number] = [point.x, point.y];
  const displayCoord = toDisplayCoordinate(point.x, point.y);

  return {
    grid: {
      x: gridCoord[0],
      y: gridCoord[1],
      formatted: `(${gridCoord[0]},${gridCoord[1]})`
    },
    display: {
      x: displayCoord[0],
      y: displayCoord[1],
      formatted: `(${displayCoord[0]},${displayCoord[1]})`
    },
    isCenter: displayCoord[0] === 0 && displayCoord[1] === 0
  };
};

/**
 * 根据显示坐标查找数据点
 * @param dataSet 数据集
 * @param displayX 显示X坐标
 * @param displayY 显示Y坐标
 * @returns 数据点对象，如果不存在则返回null
 */
export const getMatrixDataByDisplayCoordinate = (
  dataSet: MatrixDataSet,
  displayX: number,
  displayY: number
): MatrixDataPoint | null => {
  const [gridX, gridY] = fromDisplayCoordinate(displayX, displayY);
  return getMatrixDataByCoordinate(dataSet, gridX, gridY);
};

// ===== 坐标系统验证函数 =====

/** 坐标系统验证结果 */
export interface CoordinateSystemValidation {
  /** 是否有效 */
  isValid: boolean;
  /** 中心点网格坐标 */
  centerGrid: [number, number];
  /** 中心点显示坐标 */
  centerDisplay: [number, number];
  /** 坐标范围信息 */
  ranges: {
    grid: { min: [number, number]; max: [number, number] };
    display: { min: [number, number]; max: [number, number] };
  };
  /** 验证信息 */
  messages: string[];
}

/**
 * 验证坐标系统的一致性
 * @returns 坐标系统验证结果
 */
export const validateCoordinateSystem = (): CoordinateSystemValidation => {
  const messages: string[] = [];

  // 验证中心点
  const centerGrid: [number, number] = [GRID_CENTER.X, GRID_CENTER.Y];
  const centerDisplay = toDisplayCoordinate(centerGrid[0], centerGrid[1]);

  if (centerDisplay[0] !== 0 || centerDisplay[1] !== 0) {
    messages.push(`中心点显示坐标应为(0,0)，实际为(${centerDisplay[0]},${centerDisplay[1]})`);
  }

  // 验证边界点
  const gridMin: [number, number] = [0, 0];
  const gridMax: [number, number] = [MATRIX_SIZE - 1, MATRIX_SIZE - 1];
  const displayMin = toDisplayCoordinate(gridMin[0], gridMin[1]);
  const displayMax = toDisplayCoordinate(gridMax[0], gridMax[1]);

  // 验证双向转换
  const backToGrid = fromDisplayCoordinate(centerDisplay[0], centerDisplay[1]);
  if (backToGrid[0] !== centerGrid[0] || backToGrid[1] !== centerGrid[1]) {
    messages.push(`坐标转换不一致：${centerGrid} -> ${centerDisplay} -> ${backToGrid}`);
  }

  messages.push(`网格坐标范围：(${gridMin[0]},${gridMin[1]}) 到 (${gridMax[0]},${gridMax[1]})`);
  messages.push(`显示坐标范围：(${displayMin[0]},${displayMin[1]}) 到 (${displayMax[0]},${displayMax[1]})`);
  messages.push(`中心点：网格(${centerGrid[0]},${centerGrid[1]}) = 显示(${centerDisplay[0]},${centerDisplay[1]})`);

  return {
    isValid: centerDisplay[0] === 0 && centerDisplay[1] === 0,
    centerGrid,
    centerDisplay,
    ranges: {
      grid: { min: gridMin, max: gridMax },
      display: { min: displayMin, max: displayMax }
    },
    messages
  };
};

// ===== 数据验证和统计函数 =====

/** 数据验证结果 */
export interface ValidationResult {
  /** 是否通过验证 */
  isValid: boolean;
  /** 错误信息数组 */
  errors: string[];
  /** 警告信息数组 */
  warnings: string[];
}

/**
 * 验证数据集完整性
 * @param dataSet 要验证的数据集
 * @returns 验证结果，包含错误和警告信息
 */
export const validateMatrixDataSet = (dataSet: MatrixDataSet): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证坐标范围
  validateCoordinateRange(dataSet.points, errors);

  // 检查重复坐标
  checkDuplicateCoordinates(dataSet.points, warnings);

  // 检查组数据完整性
  checkGroupDataPresence(dataSet, warnings);

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证数据点坐标是否在有效范围内
 * @param points 数据点数组
 * @param errors 错误信息数组
 */
function validateCoordinateRange(points: MatrixDataPoint[], errors: string[]): void {
  for (const point of points) {
    if (!isValidCoordinate(point.x, point.y)) {
      errors.push(`数据点 ${point.id} 坐标 (${point.x}, ${point.y}) 超出网格范围`);
    }
  }
}

/**
 * 检查重复坐标
 * @param points 数据点数组
 * @param warnings 警告信息数组
 */
function checkDuplicateCoordinates(points: MatrixDataPoint[], warnings: string[]): void {
  const coordinateMap = new Map<string, MatrixDataPoint[]>();

  // 按坐标分组
  for (const point of points) {
    const key = coordinateKey(point.x, point.y);
    addToMapArray(coordinateMap, key, point);
  }

  // 检查重复
  for (const [coordinate, pointsAtCoord] of coordinateMap) {
    if (pointsAtCoord.length > 1) {
      const ids = pointsAtCoord.map(p => p.id).join(', ');
      warnings.push(`坐标 ${coordinate} 有 ${pointsAtCoord.length} 个重叠数据点: ${ids}`);
    }
  }
}

/**
 * 检查组数据完整性
 * @param dataSet 数据集
 * @param warnings 警告信息数组
 */
function checkGroupDataPresence(dataSet: MatrixDataSet, warnings: string[]): void {
  for (const group of ALL_GROUPS) {
    const groupData = getMatrixDataByGroup(dataSet, group);
    if (groupData.length === 0) {
      warnings.push(`组 ${group} 没有数据点`);
    }
  }
}

/** 数据集统计信息 */
export interface MatrixDataStatistics {
  /** 总数据点数量 */
  totalPoints: number;
  /** 按颜色统计 */
  colorCounts: Record<BasicColorType, number>;
  /** 按级别统计 */
  levelCounts: Record<DataLevel, number>;
  /** 按组统计 */
  groupCounts: Record<GroupType, number>;
  /** 坐标范围 */
  coordinateRange: {
    xMin: number;
    xMax: number;
    yMin: number;
    yMax: number;
  };
  /** 密度分析 */
  densityAnalysis: {
    gridUtilization: number; // 网格利用率 (0-1)
    averagePointsPerGroup: number; // 平均每组数据点数
    activeGroups: number; // 有数据的组数量
  };
}

/**
 * 获取数据集统计信息
 * @param dataSet 数据集
 * @returns 详细的统计信息
 */
export const getMatrixDataStatistics = (dataSet: MatrixDataSet): MatrixDataStatistics => {
  const coordinateRange = calculateCoordinateRange(dataSet.points);
  const densityAnalysis = calculateDensityAnalysis(dataSet);

  return {
    totalPoints: dataSet.metadata.totalPoints,
    colorCounts: dataSet.metadata.colorCounts,
    levelCounts: dataSet.metadata.levelCounts,
    groupCounts: dataSet.metadata.groupCounts,
    coordinateRange,
    densityAnalysis
  };
};

/**
 * 计算坐标范围
 * @param points 数据点数组
 * @returns 坐标范围对象
 */
function calculateCoordinateRange(points: MatrixDataPoint[]) {
  if (points.length === 0) {
    return { xMin: 0, xMax: 0, yMin: 0, yMax: 0 };
  }

  const xValues = points.map(p => p.x);
  const yValues = points.map(p => p.y);

  return {
    xMin: Math.min(...xValues),
    xMax: Math.max(...xValues),
    yMin: Math.min(...yValues),
    yMax: Math.max(...yValues)
  };
}

/**
 * 计算密度分析
 * @param dataSet 数据集
 * @returns 密度分析对象
 */
function calculateDensityAnalysis(dataSet: MatrixDataSet) {
  const totalCells = MATRIX_SIZE * MATRIX_SIZE;
  const activeGroups = Object.values(dataSet.metadata.groupCounts)
    .filter(count => count > 0).length;

  return {
    gridUtilization: dataSet.metadata.totalPoints / totalCells,
    averagePointsPerGroup: activeGroups > 0
      ? Math.round(dataSet.metadata.totalPoints / activeGroups)
      : 0,
    activeGroups
  };
}

/**
 * 生成数据分布热力图数据
 * @param dataSet 数据集
 * @returns 二维数组，表示每个位置的数据点数量
 */
export const generateHeatmapData = (dataSet: MatrixDataSet): number[][] => {
  // 初始化热力图矩阵
  const heatmap: number[][] = Array(MATRIX_SIZE)
    .fill(null)
    .map(() => Array(MATRIX_SIZE).fill(0));

  // 统计每个位置的数据点数量
  for (const point of dataSet.points) {
    if (isValidCoordinate(point.x, point.y)) {
      heatmap[point.y][point.x]++;
    }
  }

  return heatmap;
};

/** 组完整性检查结果 */
export interface GroupCompletenessResult {
  /** 期望的数据点数量 */
  expectedPoints: number;
  /** 实际的数据点数量 */
  actualPoints: number;
  /** 完整性百分比 (0-100) */
  completeness: number;
  /** 缺失的数据描述 */
  missingData: string[];
}

/**
 * 检查特定组的数据完整性
 * @param group 要检查的组类型
 * @returns 完整性检查结果
 */
export const checkGroupCompleteness = (group: GroupType): GroupCompletenessResult => {
  const groupData = generateGroupData(group);
  const expectedPoints = calculateExpectedPoints();
  const missingData = findMissingData(group, groupData);

  return {
    expectedPoints,
    actualPoints: groupData.length,
    completeness: expectedPoints > 0 ? (groupData.length / expectedPoints) * 100 : 0,
    missingData
  };
};

/**
 * 计算期望的数据点总数
 * @returns 期望的数据点数量
 */
function calculateExpectedPoints(): number {
  return Object.values(GROUP_A_DATA).reduce((total, levels) => {
    return total + Object.values(levels).reduce((levelTotal, coords) => {
      return levelTotal + coords.length;
    }, 0);
  }, 0);
}

/**
 * 查找缺失的数据
 * @param group 组类型
 * @param groupData 组数据
 * @returns 缺失数据的描述数组
 */
function findMissingData(group: GroupType, groupData: MatrixDataPoint[]): string[] {
  const missingData: string[] = [];

  for (const [color, levels] of Object.entries(GROUP_A_DATA)) {
    for (const level of Object.keys(levels)) {
      const levelNum = parseInt(level);
      const hasData = groupData.some(point =>
        point.color === color && point.level === levelNum
      );

      if (!hasData) {
        missingData.push(`${group}组 ${color} 级别${level}`);
      }
    }
  }

  return missingData;
}

// ===== 预定义数据集 =====

/**
 * 获取优化的A组数据集
 * 使用延迟计算避免模块加载时的性能开销
 * @returns A组数据集
 */
export const getOptimizedGroupAData = (): MatrixDataSet => {
  return createMatrixDataSet(['A']);
};

/**
 * 获取完整的A-M组数据集
 * 使用延迟计算避免模块加载时的性能开销
 * @returns 完整的A-M组数据集
 */
export const getCompleteMatrixData = (): MatrixDataSet => {
  return createMatrixDataSet(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M']);
};

// ===== 改进的缓存机制 =====

/** 缓存条目接口 */
interface CacheEntry {
  data: MatrixDataSet;
  timestamp: number;
  version: string;
  accessCount: number;
  lastAccessed: number;
}

/** 缓存统计信息 */
interface CacheStats {
  totalEntries: number;
  totalHits: number;
  totalMisses: number;
  hitRate: number;
  memoryUsage: number; // 估算的内存使用量（字节）
}

/** 缓存管理器 */
class DataCacheManager {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    totalEntries: 0,
    totalHits: 0,
    totalMisses: 0,
    hitRate: 0,
    memoryUsage: 0
  };
  private readonly maxEntries = 10;
  private readonly maxAge = 5 * 60 * 1000; // 5分钟

  private generateCacheKey(groups: GroupType[]): string {
    return groups.sort().join('-');
  }

  private generateVersion(groups: GroupType[]): string {
    return `v1.0-${groups.length}-${Date.now()}`;
  }

  private estimateMemoryUsage(dataSet: MatrixDataSet): number {
    // 简单的内存使用估算
    return dataSet.points.length * 100; // 每个数据点约100字节
  }

  private evictOldEntries(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());

    // 删除过期条目
    for (const [key, entry] of entries) {
      if (now - entry.timestamp > this.maxAge) {
        this.cache.delete(key);
        this.stats.totalEntries--;
      }
    }

    // 如果仍然超过最大条目数，删除最少使用的条目
    if (this.cache.size >= this.maxEntries) {
      const sortedEntries = entries
        .filter(([key]) => this.cache.has(key))
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

      const toDelete = sortedEntries.slice(0, this.cache.size - this.maxEntries + 1);
      for (const [key] of toDelete) {
        this.cache.delete(key);
        this.stats.totalEntries--;
      }
    }
  }

  get(groups: GroupType[]): MatrixDataSet | null {
    const key = this.generateCacheKey(groups);
    const entry = this.cache.get(key);

    if (entry) {
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.stats.totalHits++;
      this.updateHitRate();
      return entry.data;
    }

    this.stats.totalMisses++;
    this.updateHitRate();
    return null;
  }

  set(groups: GroupType[], data: MatrixDataSet): void {
    this.evictOldEntries();

    const key = this.generateCacheKey(groups);
    const now = Date.now();
    const entry: CacheEntry = {
      data,
      timestamp: now,
      version: this.generateVersion(groups),
      accessCount: 1,
      lastAccessed: now
    };

    this.cache.set(key, entry);
    this.stats.totalEntries++;
    this.stats.memoryUsage += this.estimateMemoryUsage(data);
  }

  private updateHitRate(): void {
    const total = this.stats.totalHits + this.stats.totalMisses;
    this.stats.hitRate = total > 0 ? this.stats.totalHits / total : 0;
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  clear(): void {
    this.cache.clear();
    this.stats = {
      totalEntries: 0,
      totalHits: 0,
      totalMisses: 0,
      hitRate: 0,
      memoryUsage: 0
    };
  }
}

// 全局缓存管理器实例
const cacheManager = new DataCacheManager();

/**
 * 获取缓存的数据集（通用函数）
 * @param groups 组类型数组
 * @returns 数据集
 */
function getCachedDataSet(groups: GroupType[]): MatrixDataSet {
  let dataSet = cacheManager.get(groups);

  if (!dataSet) {
    dataSet = createMatrixDataSet(groups);
    cacheManager.set(groups, dataSet);
  }

  return dataSet;
}

/**
 * 获取缓存的A组数据集
 * @returns 缓存的A组数据集
 */
export const getCachedGroupAData = (): MatrixDataSet => {
  return getCachedDataSet(['A']);
};

/**
 * 获取缓存的完整数据集
 * @returns 缓存的完整数据集
 */
export const getCachedCompleteData = (): MatrixDataSet => {
  return getCachedDataSet(ALL_GROUPS as GroupType[]);
};

/**
 * 清除数据集缓存
 */
export const clearDataCache = (): void => {
  cacheManager.clear();
};

/**
 * 获取缓存统计信息
 * @returns 缓存统计信息
 */
export const getCacheStats = (): CacheStats => {
  return cacheManager.getStats();
};

// ===== 统一导出 =====

/**
 * 矩阵数据管理器 - 提供所有数据操作的统一接口
 */
export const MatrixDataManager = {
  // 基础数据配置
  baseData: GROUP_A_DATA,
  availableLevels: AVAILABLE_LEVELS,
  colorValues: DEFAULT_COLOR_VALUES,
  offsetConfigs: GROUP_OFFSET_CONFIGS,

  // 常量数组
  allColors: ALL_COLORS,
  allLevels: ALL_LEVELS,
  allGroups: ALL_GROUPS,

  // 数据生成
  generateGroupData,
  createDataSet: createMatrixDataSet,

  // 数据查询
  getByCoordinate: getMatrixDataByCoordinate,
  getByDisplayCoordinate: getMatrixDataByDisplayCoordinate,
  getByColor: getMatrixDataByColor,
  getByLevel: getMatrixDataByLevel,
  getByGroup: getMatrixDataByGroup,
  getByGroups: getMatrixDataByGroups,
  hasData: hasMatrixData,
  filter: filterMatrixData,

  // 映射值工具
  getMappingValue: getDataPointMappingValue,
  getColorMapping: getColorMappingValue,
  getCoordinateInfo: getDataPointCoordinateInfo,

  // 坐标转换工具函数
  toAbsolute: toAbsoluteCoordinate,
  toDisplay: toDisplayCoordinate,
  fromDisplay: fromDisplayCoordinate,
  applyOffset,
  isValid: isValidCoordinate,
  isValidDisplay: isValidDisplayCoordinate,
  validateCoordinateSystem,

  // 验证和统计
  validate: validateMatrixDataSet,
  getStatistics: getMatrixDataStatistics,
  generateHeatmap: generateHeatmapData,
  checkCompleteness: checkGroupCompleteness,

  // 缓存管理
  getGroupAData: getCachedGroupAData,
  getCompleteData: getCachedCompleteData,
  clearCache: clearDataCache,
  getCacheStats,

  // 工具函数
  createCounter: createCounterObject,
  safeArrayAccess: safeGetArray
} as const;

// 默认导出
export default MatrixDataManager;
